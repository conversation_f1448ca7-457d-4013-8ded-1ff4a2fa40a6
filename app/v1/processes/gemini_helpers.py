import asyncio
import google.generativeai as genai

# Model pricing configuration
MODEL_PRICING = {
    "gemini-2.0-flash": {
        "input_price_per_token": 0.75 * 10 ** -6,   # $0.75 per million tokens
        "output_price_per_token": 0.4 * 10 ** -6,   # $0.40 per million tokens
    },
    "gemini-2.0-flash-lite": {
        "input_price_per_token": 0.075 * 10 ** -6,  # $0.075 per million tokens
        "output_price_per_token": 0.30 * 10 ** -6,  # $0.30 per million tokens
    }
}

# Image/Video/Audio pricing configuration (per input, not per token)
MEDIA_PRICING = {
    "gemini-2.0-flash": {
        "image_price_per_input": 0.10,  # $0.10 per image
        "video_price_per_input": 0.10,  # $0.10 per video
        "audio_price_per_input": 0.70,  # $0.70 per audio
        "output_price_per_token": 0.4 * 10 ** -6,   # $0.40 per million tokens
    },
    "gemini-2.0-flash-lite": {
        "image_price_per_input": 0.0,   # Free for lite model
        "video_price_per_input": 0.0,   # Free for lite model
        "audio_price_per_input": 0.0,   # Free for lite model
        "output_price_per_token": 0.30 * 10 ** -6,  # $0.30 per million tokens
    }
}

def get_model(api_key: str, model_name: str, generation_config: dict, system_instruction: str = None):
    """
    Get a Gemini model with the specified configuration.

    Args:
        api_key: Google API key
        model_name: Model name (e.g., "gemini-2.0-flash" or "gemini-2.0-flash-lite")
        generation_config: Generation configuration dictionary
        system_instruction: Optional system instruction

    Returns:
        Configured Gemini model
    """
    genai.configure(api_key=api_key)
    return genai.GenerativeModel(
        model_name=model_name,
        generation_config=generation_config,
        system_instruction=system_instruction,
    )

async def async_upload_to_gemini(path, mime_type=None):
    loop = asyncio.get_event_loop()
    file = await loop.run_in_executor(None, lambda: genai.upload_file(path, mime_type=mime_type))
    return file

async def async_generate_content(model, file, prompt: str):
    loop = asyncio.get_event_loop()
    def _generate():
        response = model.generate_content(
            contents=[
                {
                    "role": "user",
                    "parts": [
                        file,
                        prompt,
                    ],
                }
            ]
        )
        return response
    response = await loop.run_in_executor(None, _generate)
    return response

def price_nep(response, model_name: str = "gemini-2.0-flash-lite"):
    """
    Calculate price in NEP based on model usage and model type.

    Args:
        response: Gemini API response with usage metadata
        model_name: Name of the model used (determines pricing)

    Returns:
        Price in NEP
    """
    # Get pricing for the specific model
    if model_name not in MODEL_PRICING:
        # Default to flash-lite pricing if model not found
        model_name = "gemini-2.0-flash-lite"

    pricing = MODEL_PRICING[model_name]

    price_usd = (
        response.usage_metadata.prompt_token_count * pricing["input_price_per_token"]
        + response.usage_metadata.candidates_token_count * pricing["output_price_per_token"]
    )
    price_nep = price_usd * 137
    return price_nep

def get_model_pricing(model_name: str):
    """
    Get pricing information for a specific model.

    Args:
        model_name: Name of the model

    Returns:
        Dictionary with pricing information
    """
    return MODEL_PRICING.get(model_name, MODEL_PRICING["gemini-2.0-flash-lite"])